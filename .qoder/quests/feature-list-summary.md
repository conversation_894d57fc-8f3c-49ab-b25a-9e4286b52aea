# Gooze-Vben-API 功能总结文档

## 概述
基于 Go 语言的后端管理系统 API 服务，提供完整的权限管理、系统配置、用户管理等功能，适用于后台管理系统的快速开发。

## 技术架构

### 核心框架
- **Web 框架**: Gin (v1.10.1) - HTTP 路由和中间件
- **ORM 框架**: GORM (v1.30.0) - 数据库操作
- **权限控制**: Casbin - RBAC 权限模型
- **身份验证**: JWT - 无状态身份验证
- **配置管理**: Viper - 配置文件解析
- **日志系统**: Zap - 结构化日志记录

### 数据库支持
- MySQL (主要支持)
- PostgreSQL
- SQLite
- SQL Server

### 架构模式
```mermaid
graph TB
    A[Client Request] --> B[Router Layer]
    B --> C[Middleware Layer]
    C --> D[Handler Layer]
    D --> E[Logic Layer]
    E --> F[Model Layer]
    F --> G[Database]
    
    C --> H[JWT Authentication]
    C --> I[Casbin Authorization]
    C --> J[Operation Logging]
```

## 功能模块详细分析

### 1. 认证授权模块 (Authentication & Authorization)

#### 功能特性
- JWT Token 认证
- 基于 Casbin 的 RBAC 权限控制
- 用户登录/登出
- 密码加密（盐值加密）

#### API 接口
| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 用户登录 | POST | `/api/v1/system/auth/login` | 验证用户名密码，返回JWT Token |
| 用户登出 | POST | `/api/v1/system/auth/logout` | 用户登出（TODO：未完全实现） |

#### 数据模型
```go
type LoginReq struct {
    Username string `json:"username" binding:"required"`
    Password string `json:"password" binding:"required"`
}

type LoginResp struct {
    Id          int64    `json:"id"`
    Username    string   `json:"username"`
    RealName    string   `json:"realName"`
    Roles       []string `json:"roles"`
    AccessToken string   `json:"accessToken"`
}
```

#### 权限模型 (Casbin RBAC)
```
[request_definition]
r = sub, obj, act

[policy_definition] 
p = sub, obj, act

[role_definition]
g = _, _

[matchers]
m = g(r.sub, p.sub) && r.obj == p.obj && r.act == p.act
```

### 2. 用户管理模块 (User Management)

#### 功能特性
- 用户 CRUD 操作
- 用户状态管理（启用/禁用）
- 用户角色分配
- 用户信息查询（支持分页、搜索）

#### API 接口
| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 获取用户信息 | GET | `/api/v1/user/info` | 获取当前登录用户信息 |
| 添加用户 | POST | `/api/v1/user/add` | 创建新用户 |
| 用户列表 | GET | `/api/v1/user/list` | 分页查询用户列表 |
| 更新用户 | PUT | `/api/v1/user/update/:id` | 更新用户信息 |
| 删除用户 | DELETE | `/api/v1/user/delete/:id` | 删除用户 |

#### 数据模型
```go
type SysUsers struct {
    GnModel
    Username      string `json:"username"`      // 用户名
    Nickname      string `json:"nickname"`      // 昵称  
    Password      string `json:"password"`      // 密码
    Salt          string `json:"salt"`          // 加密盐
    Mobile        string `json:"mobile"`        // 手机号
    Gender        int64  `json:"gender"`        // 性别
    Email         string `json:"email"`         // 邮箱
    Avatar        string `json:"avatar"`        // 头像
    Status        int64  `json:"status"`        // 状态
    RoleId        int64  `json:"roleId"`        // 角色ID
    LastLoginTime int64  `json:"lastLoginTime"` // 最后登录时间
    LastLoginIp   string `json:"lastLoginIp"`   // 最后登录IP
}
```

### 3. 角色管理模块 (Role Management)

#### 功能特性
- 角色 CRUD 操作
- 角色权限分配（菜单权限 + API 权限）
- 角色状态管理
- 角色信息查询

#### API 接口
| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 添加角色 | POST | `/api/v1/role/add` | 创建新角色 |
| 角色列表 | GET | `/api/v1/role/list` | 分页查询角色列表 |
| 角色详情 | GET | `/api/v1/role/info/:id` | 获取角色详细信息 |
| 更新角色 | PUT | `/api/v1/role/update/:id` | 更新角色信息 |
| 分配权限 | PUT | `/api/v1/role/assign/:id` | 为角色分配菜单和API权限 |
| 删除角色 | DELETE | `/api/v1/role/delete/:id` | 删除角色 |

#### 数据模型
```go
type SysRoles struct {
    GnModel
    Name      string `json:"name"`      // 角色名称
    Code      string `json:"code"`      // 角色编码
    Sort      int64  `json:"sort"`      // 排序
    Status    int64  `json:"status"`    // 状态
    Remark    string `json:"remark"`    // 备注
}
```

### 4. 菜单管理模块 (Menu Management)

#### 功能特性
- 菜单树形结构管理
- 菜单路由配置
- 菜单权限控制
- 动态路由生成

#### API 接口
| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 获取路由 | GET | `/api/v1/menu/router` | 获取用户可访问的路由信息 |
| 菜单树 | GET | `/api/v1/menu/tree` | 获取菜单树形结构 |
| 添加菜单 | POST | `/api/v1/menu/add` | 创建新菜单 |
| 更新菜单 | PUT | `/api/v1/menu/update/:id` | 更新菜单信息 |
| 菜单详情 | GET | `/api/v1/menu/info/:id` | 获取菜单详细信息 |
| 删除菜单 | DELETE | `/api/v1/menu/delete/:id` | 删除菜单 |

#### 数据模型
```go
type SysMenus struct {
    GnModel
    ParentId           int64  `json:"parentId"`           // 父菜单ID
    Name               string `json:"name"`               // 菜单名称
    Type               string `json:"type"`               // 菜单类型
    RouteName          string `json:"routeName"`          // 路由名称
    Path               string `json:"path"`               // 路由路径
    Component          string `json:"component"`          // 组件路径
    Perm               string `json:"perm"`               // 权限标识
    Status             int64  `json:"status"`             // 显示状态
    Icon               string `json:"icon"`               // 图标
    Sort               int64  `json:"sort"`               // 排序
}
```

### 5. API 管理模块 (API Management)

#### 功能特性
- API 接口管理
- 接口权限控制
- 接口分类管理
- 接口监控

#### API 接口
| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 添加 API | POST | `/api/v1/api/add` | 创建新 API |
| API 列表 | GET | `/api/v1/api/list` | 分页查询 API 列表 |
| 更新 API | PUT | `/api/v1/api/update/:id` | 更新 API 信息 |
| 删除 API | DELETE | `/api/v1/api/delete/:id` | 删除 API |

#### 数据模型
```go
type SysApis struct {
    GnModel
    ParentId    int64  `json:"parentId"`    // 父ID
    Description string `json:"description"` // 描述
    Method      string `json:"method"`      // 请求方法
    Path        string `json:"path"`        // 请求路径
}
```

### 6. 数据字典模块 (Dictionary Management)

#### 功能特性
- 系统配置项管理
- 字典数据 CRUD
- 字典分类管理
- 前端下拉选项数据源

#### API 接口
| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 添加字典 | POST | `/api/v1/dict/add` | 创建新字典项 |
| 字典列表 | GET | `/api/v1/dict/list` | 分页查询字典列表 |
| 更新字典 | PUT | `/api/v1/dict/update/:id` | 更新字典信息 |
| 删除字典 | DELETE | `/api/v1/dict/delete/:id` | 删除字典项 |

#### 数据模型
```go
type SysDicts struct {
    GnModel
    DictName  string `json:"dictName"`  // 字典名称
    DictType  string `json:"dictType"`  // 字典类型
    ItemKey   string `json:"itemKey"`   // 字典编码
    ItemValue string `json:"itemValue"` // 字典值
    Sort      int64  `json:"sort"`      // 排序
    Status    int64  `json:"status"`    // 状态
    Remark    string `json:"remark"`    // 备注
}
```

### 7. 操作日志模块 (Operation Log)

#### 功能特性
- 自动记录用户操作
- 请求响应内容记录
- 操作耗时统计
- 日志查询和删除

#### API 接口
| 接口 | 方法 | 路径 | 功能 |
|------|------|------|------|
| 操作日志列表 | GET | `/api/v1/record/list` | 分页查询操作日志 |
| 删除日志 | DELETE | `/api/v1/record/delete/:id` | 删除操作日志 |

#### 数据模型
```go
type SysRecords struct {
    GnModel
    Username    string `json:"username"`    // 用户名
    UserId      int64  `json:"userId"`      // 用户ID
    Description string `json:"description"` // 操作描述
    Method      string `json:"method"`      // 请求方法
    Path        string `json:"path"`        // 请求路径
    StatusCode  int64  `json:"statusCode"`  // 状态码
    Elapsed     string `json:"elapsed"`     // 耗时
    Request     string `json:"request"`     // 请求参数
    Response    string `json:"response"`    // 返回参数
    Platform    string `json:"platform"`    // 平台信息
    Ip          string `json:"ip"`          // IP地址
}
```

## 中间件系统

### 1. 认证中间件 (JWT Middleware)
- 验证 JWT Token 有效性
- 解析用户身份信息
- 设置用户上下文

### 2. 权限中间件 (Casbin Middleware)
- 基于 RBAC 模型的权限验证
- 动态权限检查
- 资源访问控制

### 3. 操作日志中间件 (Record Middleware)
- 自动记录所有 API 调用
- 请求响应内容记录
- 异步日志存储

### 4. 跨域中间件 (CORS Middleware)
- 处理跨域请求
- 配置允许的域名和方法

## 数据库设计

### 核心表结构
```mermaid
erDiagram
    SysUsers ||--o{ SysRoles : "belongs_to"
    SysRoles ||--o{ SysRoleAuths : "has_many"
    SysRoles ||--o{ SysRoleApis : "has_many"
    SysMenus ||--o{ SysRoleAuths : "referenced_by"
    SysApis ||--o{ SysRoleApis : "referenced_by"
    
    SysUsers {
        int64 id PK
        string username
        string password
        string salt
        int64 role_id FK
        int64 status
        timestamp created_at
    }
    
    SysRoles {
        int64 id PK
        string name
        string code
        int64 status
        timestamp created_at
    }
    
    SysMenus {
        int64 id PK
        int64 parent_id
        string name
        string path
        string component
        int64 status
    }
    
    SysApis {
        int64 id PK
        int64 parent_id
        string description
        string method
        string path
    }
```

## 配置系统

### 主要配置项
```yaml
app:
  name: gooze-cli
  env: debug
  addr: ":18168"
  timeout: 1

databases:
  - name: master
    driver: mysql
    dsn: "user:password@tcp(host:port)/dbname"
    useGorm: true

jwt:
  secretKey: "123456"
  expire: 86400

casbin:
  modePath: "./configs/rbac_model.conf"

log:
  path: ./logs/
  mode: both
  logrotate: false

redis:
  addr: "127.0.0.1:6379"
  password: ""
  db: 0
```

## 部署要求

### 运行环境
- Go 1.24.1+
- MySQL 5.7+/PostgreSQL/SQLite
- Redis (可选)

### 构建部署
```bash
# 安装依赖
go mod tidy

# 启动服务
sh ./build/scripts/start_server.sh

# 代码生成
sh ./build/scripts/gen_server.sh
```

## 扩展功能建议

### 1. 文件上传管理
- 支持多种文件存储方式（本地、OSS、S3）
- 文件类型和大小限制
- 图片处理和压缩

### 2. 系统监控
- 系统性能监控
- API 调用统计
- 错误日志监控

### 3. 消息通知
- 站内消息系统
- 邮件/短信通知
- WebSocket 实时通知

### 4. 数据备份
- 自动数据备份
- 备份恢复功能
- 数据迁移工具

### 5. 多租户支持
- 租户数据隔离
- 租户配置管理
- 资源配额限制